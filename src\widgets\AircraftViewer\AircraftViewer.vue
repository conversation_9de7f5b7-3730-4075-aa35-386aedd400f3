<template>
  <div class="mainContainer">
    <div id="cesium-viewer" ref="viewerContainer"></div>
    <!-- 视角切换控制面板 -->
    <div class="camera-controls">
      <button
        @click="toggleCameraMode"
        :class="['camera-btn', { active: isFirstPersonView }]"
      >
        {{ isFirstPersonView ? "第一视角" : "第三视角" }}
      </button>
      <button @click="resetAnimation" class="reset-btn">重置动画</button>
      <!-- 新增暂停/继续按钮 -->
      <button @click="togglePause" class="pause-btn">
        {{ isPaused ? "继续" : "暂停" }}
      </button>
      <div v-if="isFirstPersonView" class="first-person-tips">
        <small>第一视角模式：</small>
        <small>• 尾翼视角观察</small>
        <small>• 视角平行于地面</small>
        <small>• 跟随飞机航向</small>
        <small>• 鼠标拖拽旋转视角</small>
      </div>
    </div>
    <!-- 新增：相机视角信息显示 -->
    <div class="camera-info-panel">
      <div>
        <b>相机视角信息</b>
      </div>
      <div>经度: {{ cameraInfo.lon.toFixed(6) }}°</div>
      <div>纬度: {{ cameraInfo.lat.toFixed(6) }}°</div>
      <div>高度: {{ cameraInfo.height.toFixed(2) }} m</div>
      <div>Heading: {{ cameraInfo.heading.toFixed(2) }}°</div>
      <div>Pitch: {{ cameraInfo.pitch.toFixed(2) }}°</div>
      <div>Roll: {{ cameraInfo.roll.toFixed(2) }}°</div>
    </div>

    <!-- 时间轴控制面板 -->
    <div class="timeline-panel">
      <div class="timeline-container">
        <!-- 播放控制按钮 -->
        <div class="timeline-controls">
          <button @click="togglePause" class="play-pause-btn">
            <svg
              v-if="isPaused"
              class="w-5 h-5"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                clip-rule="evenodd"
              />
            </svg>
            <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
          <button @click="resetAnimation" class="reset-btn-timeline">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
        </div>

        <!-- 时间显示 -->
        <div class="time-display">
          <span class="current-time">{{ formatTime(currentTimeSeconds) }}</span>
          <span class="time-separator">/</span>
          <span class="total-time">{{ formatTime(totalTimeSeconds) }}</span>
        </div>

        <!-- 时间轴滑块 -->
        <div class="timeline-slider-container">
          <input
            type="range"
            class="timeline-slider"
            :min="0"
            :max="totalTimeSeconds"
            :value="currentTimeSeconds"
            @input="onTimelineChange"
            @mousedown="onTimelineDragStart"
            @mouseup="onTimelineDragEnd"
          />
          <div class="timeline-track">
            <div
              class="timeline-progress"
              :style="{
                width: `${(currentTimeSeconds / totalTimeSeconds) * 100}%`,
              }"
            ></div>
          </div>
        </div>

        <!-- 速度控制 -->
        <div class="speed-control">
          <span class="speed-label">速度:</span>
          <select
            v-model="playbackSpeed"
            @change="onSpeedChange"
            class="speed-select"
          >
            <option value="0.25">0.25x</option>
            <option value="0.5">0.5x</option>
            <option value="1">1x</option>
            <option value="2">2x</option>
            <option value="4">4x</option>
            <option value="8">8x</option>
          </select>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as Cesium from "cesium";

import { onBeforeUnmount, onMounted, reactive, ref } from "vue";

import { createModel } from "@/utils/createModel";
import { initMap } from "@/utils/initMap";

const viewerContainer = ref<HTMLElement | null>(null);
let viewer: Cesium.Viewer | null = null;
const planURL = "/src/assets/models/plan1.glb";

let aircraftEntity: Cesium.Entity | null = null;
let animationInterval: number | null = null;

// 视角控制相关变量
const isFirstPersonView = ref(false);
let cameraUpdateListener: Cesium.Event.RemoveCallback | null = null;

const cameraInfo = reactive({
  lon: 0,
  lat: 0,
  height: 0,
  heading: 0,
  pitch: 0,
  roll: 0,
});

let cameraChangedRemoveCallback: (() => void) | null = null;

// 在北京天安门上空创建飞机模型，设置朝向
// createModel(
//   viewer,
//   planURL,
//   2000, // 高度2000米
//   116.4074, // 北京经度
//   39.9042, // 北京纬度
//   225, // 朝向角度，使飞机朝向东南方
//   10, // 50, // 机头略微向上
//   0, // 0, // 不倾斜
// );

const initViewer = async () => {
  viewer = await initMap(viewerContainer);
  viewer.entities.removeAll();

  const startLon = 116.3975; // 天安门
  const startLat = 39.9087;
  const endLon = 116.3269; // 清华大学
  const endLat = 40.0032;

  // 定义飞行关键高度点：起飞 -> 上升 -> 巡航 -> 下降
  const heights = [100, 800, 1500, 2000, 2000, 2000, 2000, 2000];
  const pointCount = heights.length;
  const secondsBetweenPoints = 3;

  const start = Cesium.JulianDate.now();
  const position = new Cesium.SampledPositionProperty();

  // 设置插值算法为 Hermite，多点之间平滑曲线
  position.setInterpolationOptions({
    interpolationDegree: 5,
    interpolationAlgorithm: Cesium.HermitePolynomialApproximation,
  });

  for (let i = 0; i < pointCount; i++) {
    const lon = startLon + ((endLon - startLon) * i) / (pointCount - 1);
    const lat = startLat + ((endLat - startLat) * i) / (pointCount - 1);
    const height = heights[i];

    const time = Cesium.JulianDate.addSeconds(
      start,
      i * secondsBetweenPoints,
      new Cesium.JulianDate(),
    );
    const positionSample = Cesium.Cartesian3.fromDegrees(lon, lat, height);
    position.addSample(time, positionSample);
  }

  const stop = Cesium.JulianDate.addSeconds(
    start,
    (pointCount - 1) * secondsBetweenPoints,
    new Cesium.JulianDate(),
  );

  aircraftEntity = viewer.entities.add({
    availability: new Cesium.TimeIntervalCollection([
      new Cesium.TimeInterval({ start, stop }),
    ]),
    position,
    orientation: new Cesium.VelocityOrientationProperty(position),
    model: {
      uri: planURL,
      minimumPixelSize: 64,
      maximumScale: 200,
      scale: 2,
    },
  });

  viewer.clock.startTime = start.clone();
  viewer.clock.stopTime = stop.clone();
  viewer.clock.currentTime = start.clone();
  viewer.clock.clockRange = Cesium.ClockRange.CLAMPED;
  viewer.clock.multiplier = 1;
  viewer.clock.shouldAnimate = true;

  // 默认使用第三视角（跟踪模式）
  viewer.trackedEntity = aircraftEntity;

  // 设置初始相机视角
  // viewer.camera.setView({
  //   destination: Cesium.Cartesian3.fromDegrees(116.45, 39.9042, 5000),
  //   orientation: {
  //     heading: Cesium.Math.toRadians(90),
  //     pitch: Cesium.Math.toRadians(-45),
  //     roll: 0.0,
  //   },
  // });
};

// 第一视角相机更新函数
const updateFirstPersonCamera = () => {
  if (!aircraftEntity || !viewer) return;

  const position = aircraftEntity.position?.getValue(viewer.clock.currentTime);
  const orientation = aircraftEntity.orientation?.getValue(
    viewer.clock.currentTime,
  );

  if (position && orientation) {
    // 获取飞机的朝向角度
    const hpr = Cesium.HeadingPitchRoll.fromQuaternion(orientation);

    // 计算相机在飞机坐标系中的偏移位置（尾翼位置）
    const localOffset = new Cesium.Cartesian3(0, -15, 3); // 后方15米，上方3米

    // 将本地偏移转换为世界坐标
    const transform = Cesium.Transforms.headingPitchRollToFixedFrame(
      position,
      hpr,
    );
    const worldOffset = Cesium.Matrix4.multiplyByPoint(
      transform,
      localOffset,
      new Cesium.Cartesian3(),
    );

    // 设置相机朝向：与飞机朝向一致，但俯仰角设为0（平行于地面）
    const cameraHeading = hpr.heading; // 保持飞机的航向
    const cameraPitch = 0; // 平行于地面
    const cameraRoll = 0; // 不倾斜

    // 设置相机位置和朝向
    viewer.scene.camera.setView({
      destination: worldOffset,
      orientation: {
        heading: cameraHeading,
        pitch: cameraPitch,
        roll: cameraRoll,
      },
    });
  }
};

// 切换视角模式
const toggleCameraMode = () => {
  if (!viewer || !aircraftEntity) return;

  isFirstPersonView.value = !isFirstPersonView.value;

  if (isFirstPersonView.value) {
    // 切换到第一视角
    viewer.trackedEntity = undefined;

    // 启用相机控制器，允许用户交互
    viewer.scene.screenSpaceCameraController.enableRotate = true;
    viewer.scene.screenSpaceCameraController.enableZoom = true;
    viewer.scene.screenSpaceCameraController.enableTilt = true;
    viewer.scene.screenSpaceCameraController.enableLook = true;

    // 移除之前的监听器
    if (cameraUpdateListener) {
      cameraUpdateListener();
      cameraUpdateListener = null;
    }

    // 立即设置第一视角位置
    updateFirstPersonCamera();

    // 添加第一视角相机更新监听器（仅更新位置，不锁定朝向）
    cameraUpdateListener = viewer.clock.onTick.addEventListener(() => {
      if (!aircraftEntity || !viewer) return;

      const position = aircraftEntity.position?.getValue(
        viewer.clock.currentTime,
      );
      const orientation = aircraftEntity.orientation?.getValue(
        viewer.clock.currentTime,
      );

      if (position && orientation) {
        // 获取飞机的朝向角度
        const hpr = Cesium.HeadingPitchRoll.fromQuaternion(orientation);

        // 计算相机在飞机坐标系中的偏移位置（尾翼位置）
        const localOffset = new Cesium.Cartesian3(0, -15, 3); // 后方15米，上方3米

        // 将本地偏移转换为世界坐标
        const transform = Cesium.Transforms.headingPitchRollToFixedFrame(
          position,
          hpr,
        );
        const worldOffset = Cesium.Matrix4.multiplyByPoint(
          transform,
          localOffset,
          new Cesium.Cartesian3(),
        );

        // 更新相机位置，并设置朝向为平行于地面
        viewer.scene.camera.position = worldOffset;

        // 设置相机朝向：与飞机朝向一致，但平行于地面
        viewer.scene.camera.setView({
          destination: worldOffset,
          orientation: {
            heading: hpr.heading, // 保持飞机的航向
            pitch: 0, // 平行于地面
            roll: 0, // 不倾斜
          },
        });
      }
    });
  } else {
    // 切换到第三视角
    if (cameraUpdateListener) {
      cameraUpdateListener();
      cameraUpdateListener = null;
    }

    // 恢复跟踪模式
    viewer.trackedEntity = aircraftEntity;

    // 恢复默认相机控制设置
    viewer.scene.screenSpaceCameraController.enableRotate = true;
    viewer.scene.screenSpaceCameraController.enableZoom = true;
    viewer.scene.screenSpaceCameraController.enableTilt = true;
    viewer.scene.screenSpaceCameraController.enableLook = true;
  }
};

// 重置动画
const resetAnimation = () => {
  if (!viewer) return;

  viewer.clock.currentTime = viewer.clock.startTime.clone();
  viewer.clock.shouldAnimate = true;
  isPaused.value = false;

  // 重置时间轴显示
  currentTimeSeconds.value = 0;

  // 如果是第一视角，重新设置相机
  if (isFirstPersonView.value) {
    updateFirstPersonCamera();
  }
};

const isPaused = ref(false);

// 时间轴相关变量
const currentTimeSeconds = ref(0);
const totalTimeSeconds = ref(0);
const playbackSpeed = ref(1);
let isDragging = ref(false);
let timeUpdateListener: Cesium.Event.RemoveCallback | null = null;

const togglePause = () => {
  if (!viewer) return;
  isPaused.value = !isPaused.value;
  viewer.clock.shouldAnimate = !isPaused.value;
};

// 格式化时间显示
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
};

// 时间轴拖动开始
const onTimelineDragStart = () => {
  isDragging.value = true;
  if (viewer) {
    viewer.clock.shouldAnimate = false;
  }
};

// 时间轴拖动结束
const onTimelineDragEnd = () => {
  isDragging.value = false;
  if (viewer && !isPaused.value) {
    viewer.clock.shouldAnimate = true;
  }
};

// 时间轴变化处理
const onTimelineChange = (event: Event) => {
  if (!viewer) return;

  const target = event.target as HTMLInputElement;
  const newTimeSeconds = parseFloat(target.value);
  currentTimeSeconds.value = newTimeSeconds;

  // 计算新的时间点
  const startTime = viewer.clock.startTime;
  const newTime = Cesium.JulianDate.addSeconds(
    startTime,
    newTimeSeconds,
    new Cesium.JulianDate(),
  );

  viewer.clock.currentTime = newTime;

  // 如果是第一视角，更新相机位置
  if (isFirstPersonView.value) {
    updateFirstPersonCamera();
  }
};

// 速度变化处理
const onSpeedChange = () => {
  if (!viewer) return;
  viewer.clock.multiplier = parseFloat(playbackSpeed.value.toString());
};

// 更新时间显示
const updateTimeDisplay = () => {
  if (!viewer || isDragging.value) return;

  const currentTime = viewer.clock.currentTime;
  const startTime = viewer.clock.startTime;
  const stopTime = viewer.clock.stopTime;

  const currentSeconds = Cesium.JulianDate.secondsDifference(
    currentTime,
    startTime,
  );
  const totalSeconds = Cesium.JulianDate.secondsDifference(stopTime, startTime);

  currentTimeSeconds.value = Math.max(
    0,
    Math.min(currentSeconds, totalSeconds),
  );
  totalTimeSeconds.value = totalSeconds;
};

onMounted(() => {
  initViewer().then(() => {
    if (viewer) {
      // 初始化相机信息
      const updateCameraInfo = () => {
        const camera = viewer!.scene.camera;
        const carto = Cesium.Cartographic.fromCartesian(camera.positionWC);
        cameraInfo.lon = Cesium.Math.toDegrees(carto.longitude);
        cameraInfo.lat = Cesium.Math.toDegrees(carto.latitude);
        cameraInfo.height = carto.height;
        cameraInfo.heading = Cesium.Math.toDegrees(camera.heading);
        cameraInfo.pitch = Cesium.Math.toDegrees(camera.pitch);
        cameraInfo.roll = Cesium.Math.toDegrees(camera.roll);
      };
      updateCameraInfo();
      // 监听相机变化
      cameraChangedRemoveCallback =
        viewer.scene.camera.changed.addEventListener(updateCameraInfo);

      // 初始化时间轴
      const startTime = viewer.clock.startTime;
      const stopTime = viewer.clock.stopTime;
      totalTimeSeconds.value = Cesium.JulianDate.secondsDifference(
        stopTime,
        startTime,
      );

      // 监听时间变化
      timeUpdateListener = viewer.clock.onTick.addEventListener(() => {
        updateTimeDisplay();
      });

      // 初始化时间显示
      updateTimeDisplay();
    }
  });
});

onBeforeUnmount(() => {
  if (cameraUpdateListener) {
    cameraUpdateListener();
  }
  if (cameraChangedRemoveCallback) {
    cameraChangedRemoveCallback();
    cameraChangedRemoveCallback = null;
  }
  if (timeUpdateListener) {
    timeUpdateListener();
    timeUpdateListener = null;
  }
  if (viewer) {
    viewer.destroy();
  }
  if (animationInterval) {
    clearInterval(animationInterval);
  }
});
</script>

<style scoped>
.mainContainer {
  position: relative;
  width: 100%;
  height: 100vh;
}

#cesium-viewer {
  width: 100%;
  height: 100%;
}

.camera-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1000;
}

.camera-btn,
.reset-btn,
.pause-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  background: rgba(42, 42, 42, 0.8);
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.camera-btn:hover,
.reset-btn:hover,
.pause-btn:hover {
  background: rgba(42, 42, 42, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.camera-btn.active {
  background: rgba(0, 122, 255, 0.8);
  border-color: rgba(0, 122, 255, 0.3);
}

.camera-btn.active:hover {
  background: rgba(0, 122, 255, 0.9);
}

.first-person-tips {
  background: rgba(42, 42, 42, 0.9);
  border-radius: 6px;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.first-person-tips small {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  line-height: 1.4;
  margin: 2px 0;
}

.first-person-tips small:first-child {
  color: rgba(0, 122, 255, 0.9);
  font-weight: 500;
  margin-bottom: 4px;
}

.camera-info-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(42, 42, 42, 0.85);
  color: #fff;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 13px;
  z-index: 1001;
  min-width: 180px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);
  border: 1px solid rgba(255, 255, 255, 0.08);
  user-select: text;
}

.camera-info-panel b {
  color: #00aaff;
  font-size: 14px;
  margin-bottom: 4px;
  display: block;
}

/* 时间轴面板样式 */
.timeline-panel {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  width: 90%;
  max-width: 800px;
}

.timeline-container {
  background-color: rgba(17, 24, 39, 0.9);
  backdrop-filter: blur(12px);
  border-radius: 8px;
  border: 1px solid rgb(55, 65, 81);
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.timeline-controls {
  display: flex;
  gap: 8px;
}

.play-pause-btn,
.reset-btn-timeline {
  width: 40px;
  height: 40px;
  background-color: rgb(37, 99, 235);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.play-pause-btn:hover,
.reset-btn-timeline:hover {
  background-color: rgb(29, 78, 216);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

.time-display {
  color: white;
  font-family: monospace;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 80px;
}

.current-time {
  color: rgb(96, 165, 250);
  font-weight: 600;
}

.time-separator {
  color: rgb(156, 163, 175);
}

.total-time {
  color: rgb(209, 213, 219);
}

.timeline-slider-container {
  flex: 1;
  position: relative;
  height: 20px;
  display: flex;
  align-items: center;
}

.timeline-track {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  transform: translateY(-50%);
  pointer-events: none;
}

.timeline-progress {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
  border-radius: 2px;
  transition: width 0.1s ease;
}

.timeline-slider {
  width: 100%;
  height: 20px;
  background: transparent;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.timeline-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.timeline-slider::-webkit-slider-thumb:hover {
  background: #2563eb;
  transform: scale(1.2);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

.timeline-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.timeline-slider::-moz-range-thumb:hover {
  background: #2563eb;
  transform: scale(1.2);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

.speed-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.speed-label {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.speed-select {
  background-color: rgb(31, 41, 55);
  color: white;
  border: 1px solid rgb(75, 85, 99);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  outline: none;
  cursor: pointer;
}

.speed-select:focus {
  border-color: rgb(59, 130, 246);
  box-shadow: 0 0 0 1px rgb(59, 130, 246);
}

.speed-select option {
  background-color: rgb(31, 41, 55);
  color: white;
}
</style>
